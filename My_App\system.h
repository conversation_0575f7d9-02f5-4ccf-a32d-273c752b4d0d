#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdint.h"
#include "stdlib.h"

#include "main.h"
#include "usart.h"
#include "i2c.h"
#include "math.h"
#include "adc.h"
#include "tim.h"
#include "dac.h"
#include "scheduler.h"
#include "ringbuffer.h"
#include "oled.h"
#include "u8g2.h"
#include "WouoUI.h"      
#include "WouoUI_user.h" 
#include "gd25qxx.h"


#include "oled_app.h"
#include "led_app.h"
#include "ebtn_app.h"
#include "ringbuffer.h"
#include "uart_app.h"
#include "adc_app.h"
#include "flash_app.h"

extern uint8_t ucLed[6];
extern uint16_t uart_rx_index;
extern uint32_t uart_rx_ticks;
extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_dma_buffer[128];
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];
extern u8g2_t u8g2;
extern RTC_HandleTypeDef hrtc;

