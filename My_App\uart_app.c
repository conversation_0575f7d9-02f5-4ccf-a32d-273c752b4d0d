#include "uart_app.h"
#include "selftest_app.h"
#include "config_app.h"
#include "stdlib.h"
#include "stdarg.h"
#include "string.h"
#include "stdio.h"
#include "usart.h"

#define UART_TIMEOUT_MS 100
uint16_t uart_rx_index = 0;
uint32_t uart_rx_ticks = 0;
uint8_t uart_rx_buffer[128] = {0};
uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};
uint8_t uart_flag = 0;
struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];

// 串口交互状态机全局变量 - 支持交互式输入
static uart_state_t uart_current_state = UART_STATE_IDLE;

//??????????? `my_printf` ?????????????? `printf` ??????????????????? UART ????
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512]; // ???????????????????
	va_list arg;      // ??????????
	int len;          // ?????????????

	va_start(arg, format);
	// ???????????????? buffer
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// ??? HAL ???? buffer ????????
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

//?????????????????????????????????? CPU ???????"????"???
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if (huart->Instance == USART1)
	{
		uart_rx_ticks = uwTick;
		uart_rx_index++;
		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);
	}
}

/**
 * @brief UART DMA?????????????????????
 * @param huart UART???
 * @param Size ??????????????DMA???????????????????????
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 1. ?????????? (USART1)
    if (huart->Instance == USART1)
    {
			/*
        // 2. ??????????? DMA ???? (????????????)
        //    ??????????????????????????????? DMA ????????????
        HAL_UART_DMAStop(huart);

        // 3. ?? DMA ?????????????????? (Size ?????) ?????????????????
        memcpy(uart_dma_buffer, uart_rx_dma_buffer, Size); 
        // ???????????? Size????????????????????
        
        // 4. ????"????????"??????????????????????
        uart_flag = 1;

        // 5. ??? DMA ????????????????????????
        //    ??? memcpy ??????? Size ?????????????????????????
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        // 6. **?????????????????? DMA ????????**
        //    ???????????????????????????
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
        
        // 7. ????????????????????????????????????? (???????)
        // __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
				*/
				HAL_UART_DMAStop(huart);

			rt_ringbuffer_put(&uart_ringbuffer,uart_rx_dma_buffer,Size);
			memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));
			HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
			__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
			
    }
}


void uart_task(void)
{
  /*  
	// 1. ?????????????????0?????????????????????
	if (uart_rx_index == 0)
		return;

    // 2. ?????????????? - ????????? > ??????????
	if (uwTick - uart_rx_ticks > UART_TIMEOUT_MS) // ????????
	{
        // --- 3. ???????????? --- 
        // "uart_rx_buffer" ????0?????? "uart_rx_index - 1" ??
        // ????????????????????????????
		my_printf(&huart1, "uart data: %s\n", uart_rx_buffer);
        // (??????????????????????????????????????LED)
        // --- ???????? --- 

		// 4. ????????????????????????????????????????
		memset(uart_rx_buffer, 0, uart_rx_index);
		uart_rx_index = 0;
		huart1.pRxBuffPtr = uart_rx_buffer;
	}
    // ??????????????????????????
	
	 if(uart_flag == 0) 
        return; // ????????????????????????????
    
    // 2. ???????????????????????????
    //    ??????????????????
    uart_flag = 0;
	
    // 3. ???? "??????????" (uart_dma_buffer) ????????
    //    ?????????????????????????????????????????
    printf("DMA data: %s\n", uart_dma_buffer);
    //    (?????????????????????????????????????????????????)
    
    // 4. ???"??????????"???????????????
    memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
	 */
	 uint16_t length;
	
	length = rt_ringbuffer_data_len(&uart_ringbuffer);
	
	if(length == 0) return;
	
	rt_ringbuffer_get(&uart_ringbuffer,uart_dma_buffer,length);

	// ??????? - ???"test"?????????
	if (strstr((char*)uart_dma_buffer, "test") != NULL) {
		system_selftest();  // 调用系统自检函数
	} else if (strstr((char*)uart_dma_buffer, "config save") != NULL) {
		// config save命令 - 保存配置到Flash
		if (config_save_to_flash() == CONFIG_STATUS_OK) {
			config_print_save_result();  // 显示保存结果
		} else {
			my_printf(&huart1, "config save failed\r\n");
		}
	} else if (strstr((char*)uart_dma_buffer, "config read") != NULL) {
		// config read命令 - 从Flash读取配置
		if (config_load_from_flash() == CONFIG_STATUS_OK) {
			config_print_read_result();  // 显示读取结果
		} else {
			my_printf(&huart1, "config read failed\r\n");
		}
	} else if (strstr((char*)uart_dma_buffer, "ratio") != NULL) {
		// ratio命令 - 进入交互式ratio设置模式
		config_start_ratio_input();
		uart_current_state = UART_STATE_WAIT_RATIO;
	} else if (strstr((char*)uart_dma_buffer, "limit") != NULL) {
		// limit命令 - 进入交互式limit设置模式
		config_start_limit_input();
		uart_current_state = UART_STATE_WAIT_LIMIT;
	} else if (strstr((char*)uart_dma_buffer, "conf") != NULL) {
		// conf命令 - 从INI文件读取配置
		if (config_read_from_ini() == CONFIG_STATUS_OK) {
			// INI文件读取成功，显示读取到的参数
			my_printf(&huart1, "Ratio = %.1f\r\n", config_get_ratio());
			my_printf(&huart1, "Limit = %.1f\r\n", config_get_limit());
			my_printf(&huart1, "config read success\r\n");
		} else {
			// INI文件不存在或读取失败
			config_print_ini_not_found();
		}
	} else if (uart_current_state != UART_STATE_IDLE) {
		// 处理交互式输入状态下的用户输入
		config_process_input((char*)uart_dma_buffer);
		uart_current_state = UART_STATE_IDLE;  // 处理完成后返回空闲状态
	} else {
		// 保持原有的数据回显功能
		my_printf(&huart1, "uart data: %s\n", uart_dma_buffer);
	}

	// ?????????????ν??????
	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));

}



