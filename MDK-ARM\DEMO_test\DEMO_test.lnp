--cpu=Cortex-M4.fp.sp
"demo_test\startup_stm32f429xx.o"
"demo_test\main.o"
"demo_test\gpio.o"
"demo_test\adc.o"
"demo_test\dac.o"
"demo_test\dma.o"
"demo_test\i2c.o"
"demo_test\rtc.o"
"demo_test\sdio.o"
"demo_test\spi.o"
"demo_test\tim.o"
"demo_test\usart.o"
"demo_test\stm32f4xx_it.o"
"demo_test\stm32f4xx_hal_msp.o"
"demo_test\stm32f4xx_hal_adc.o"
"demo_test\stm32f4xx_hal_adc_ex.o"
"demo_test\stm32f4xx_ll_adc.o"
"demo_test\stm32f4xx_hal_rcc.o"
"demo_test\stm32f4xx_hal_rcc_ex.o"
"demo_test\stm32f4xx_hal_flash.o"
"demo_test\stm32f4xx_hal_flash_ex.o"
"demo_test\stm32f4xx_hal_flash_ramfunc.o"
"demo_test\stm32f4xx_hal_gpio.o"
"demo_test\stm32f4xx_hal_dma_ex.o"
"demo_test\stm32f4xx_hal_dma.o"
"demo_test\stm32f4xx_hal_pwr.o"
"demo_test\stm32f4xx_hal_pwr_ex.o"
"demo_test\stm32f4xx_hal_cortex.o"
"demo_test\stm32f4xx_hal.o"
"demo_test\stm32f4xx_hal_exti.o"
"demo_test\stm32f4xx_hal_dac.o"
"demo_test\stm32f4xx_hal_dac_ex.o"
"demo_test\stm32f4xx_hal_i2c.o"
"demo_test\stm32f4xx_hal_i2c_ex.o"
"demo_test\stm32f4xx_hal_rtc.o"
"demo_test\stm32f4xx_hal_rtc_ex.o"
"demo_test\stm32f4xx_ll_sdmmc.o"
"demo_test\stm32f4xx_hal_sd.o"
"demo_test\stm32f4xx_hal_spi.o"
"demo_test\stm32f4xx_hal_tim.o"
"demo_test\stm32f4xx_hal_tim_ex.o"
"demo_test\stm32f4xx_hal_uart.o"
"demo_test\system_stm32f4xx.o"
"demo_test\ebtn.o"
"demo_test\ringbuffer.o"
"demo_test\oled.o"
"demo_test\mui.o"
"demo_test\mui_u8g2.o"
"demo_test\u8g2_arc.o"
"demo_test\u8g2_bitmap.o"
"demo_test\u8g2_box.o"
"demo_test\u8g2_buffer.o"
"demo_test\u8g2_button.o"
"demo_test\u8g2_circle.o"
"demo_test\u8g2_cleardisplay.o"
"demo_test\u8g2_d_memory.o"
"demo_test\u8g2_d_setup.o"
"demo_test\u8g2_font.o"
"demo_test\u8g2_fonts.o"
"demo_test\u8g2_hvline.o"
"demo_test\u8g2_input_value.o"
"demo_test\u8g2_intersection.o"
"demo_test\u8g2_kerning.o"
"demo_test\u8g2_line.o"
"demo_test\u8g2_ll_hvline.o"
"demo_test\u8g2_message.o"
"demo_test\u8g2_polygon.o"
"demo_test\u8g2_selection_list.o"
"demo_test\u8g2_setup.o"
"demo_test\u8log.o"
"demo_test\u8log_u8g2.o"
"demo_test\u8log_u8x8.o"
"demo_test\u8x8_8x8.o"
"demo_test\u8x8_byte.o"
"demo_test\u8x8_cad.o"
"demo_test\u8x8_capture.o"
"demo_test\u8x8_d_ssd1306_128x32.o"
"demo_test\u8x8_debounce.o"
"demo_test\u8x8_display.o"
"demo_test\u8x8_fonts.o"
"demo_test\u8x8_gpio.o"
"demo_test\u8x8_input_value.o"
"demo_test\u8x8_message.o"
"demo_test\u8x8_selection_list.o"
"demo_test\u8x8_setup.o"
"demo_test\u8x8_string.o"
"demo_test\u8x8_u8toa.o"
"demo_test\u8x8_u16toa.o"
"demo_test\wououi.o"
"demo_test\wououi_anim.o"
"demo_test\wououi_font.o"
"demo_test\wououi_graph.o"
"demo_test\wououi_msg.o"
"demo_test\wououi_page.o"
"demo_test\wououi_user.o"
"demo_test\wououi_win.o"
"demo_test\gd25qxx.o"
"demo_test\lfs.o"
"demo_test\lfs_port.o"
"demo_test\lfs_util.o"
"demo_test\bsp_driver_sd.o"
"demo_test\sd_diskio.o"
"demo_test\fatfs.o"
"demo_test\diskio.o"
"demo_test\ff.o"
"demo_test\ff_gen_drv.o"
"demo_test\syscall.o"
"demo_test\cc936.o"
"demo_test\scheduler.o"
"demo_test\led_app.o"
"demo_test\ebtn_app.o"
"demo_test\uart_app.o"
"demo_test\adc_app.o"
"demo_test\oled_app.o"
"demo_test\flash_app.o"
"demo_test\rtc_app.o"
"demo_test\selftest_app.o"
--library_type=microlib --strict --scatter "DEMO_test\DEMO_test.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "DEMO_test.map" -o DEMO_test\DEMO_test.axf