#ifndef __CONFIG_APP_H__
#define __CONFIG_APP_H__

#include "system.h"

// 配置管理状态定义 - 低耦合设计，便于扩展
#define CONFIG_STATUS_OK        1   // 操作成功
#define CONFIG_STATUS_ERROR     0   // 操作失败

// Flash存储地址定义 - 固定地址避免冲突
#define CONFIG_FLASH_BASE_ADDR  0x1000  // 配置存储基地址(4KB对齐)
#define CONFIG_FLASH_BACKUP_ADDR 0x2000 // 配置备份地址
#define CONFIG_FLASH_SIZE       256     // 配置数据大小(字节)

// 配置参数默认值定义
#define CONFIG_DEFAULT_RATIO    1.0f    // 默认变比值
#define CONFIG_DEFAULT_LIMIT    1.0f    // 默认阈值
#define CONFIG_DEFAULT_SAMPLING_CYCLE  5  // 默认采样周期(秒)

// 配置参数范围定义
#define CONFIG_RATIO_MIN        0.0f    // 变比最小值
#define CONFIG_RATIO_MAX        100.0f  // 变比最大值
#define CONFIG_LIMIT_MIN        0.0f    // 阈值最小值
#define CONFIG_LIMIT_MAX        500.0f  // 阈值最大值

// 采样周期范围定义
#define CONFIG_SAMPLING_CYCLE_MIN    5   // 采样周期最小值(秒)
#define CONFIG_SAMPLING_CYCLE_MAX    15  // 采样周期最大值(秒)
#define CONFIG_SAMPLING_CYCLE_5S     5   // 5秒采样周期
#define CONFIG_SAMPLING_CYCLE_10S    10  // 10秒采样周期
#define CONFIG_SAMPLING_CYCLE_15S    15  // 15秒采样周期

// 配置数据魔数和版本
#define CONFIG_MAGIC_NUMBER     0x12345678  // 配置数据魔数标识
#define CONFIG_VERSION          0x00000001  // 配置数据版本号

// INI文件相关定义
#define CONFIG_INI_FILENAME     "config.ini"    // 配置文件名
#define CONFIG_INI_LINE_MAX     128             // INI文件行最大长度

// 串口交互状态枚举 - 支持交互式输入
typedef enum {
    UART_STATE_IDLE = 0,        // 空闲状态，等待命令
    UART_STATE_WAIT_RATIO,      // 等待ratio数值输入
    UART_STATE_WAIT_LIMIT       // 等待limit数值输入
} uart_state_t;

// 配置参数结构体 - 封装所有配置数据，便于管理和扩展
typedef struct {
    uint32_t magic;             // 魔数标识，用于验证数据有效性
    float ratio;                // 变比参数 (0-100)
    float limit;                // 阈值参数 (0-500)
    uint32_t sampling_cycle;    // 采样周期参数 (5/10/15秒)
    uint32_t version;           // 配置版本号，便于后续升级兼容
    uint32_t crc;               // CRC32校验值，确保数据完整性
} config_params_t;

// 配置管理核心接口 - 低耦合设计，提供标准化接口
int config_init(void);                              // 初始化配置模块
int config_read_from_ini(void);                     // 从INI文件读取配置
int config_save_to_flash(void);                     // 保存配置到Flash
int config_load_from_flash(void);                   // 从Flash加载配置
void config_get_current(config_params_t *params);   // 获取当前配置

// 参数设置接口 - 独立的参数操作，支持验证
int config_set_ratio(float value);                  // 设置变比参数
int config_set_limit(float value);                  // 设置阈值参数
int config_set_sampling_cycle(uint32_t cycle);      // 设置采样周期参数
float config_get_ratio(void);                       // 获取当前变比
float config_get_limit(void);                       // 获取当前阈值
uint32_t config_get_sampling_cycle(void);           // 获取当前采样周期

// 交互式输入接口 - 支持串口命令交互
void config_start_ratio_input(void);                // 开始ratio输入流程
void config_start_limit_input(void);                // 开始limit输入流程
void config_process_input(const char *input);       // 处理用户输入

// 格式化输出接口 - 分离输出逻辑，便于扩展不同输出方式
void config_print_current_params(void);             // 显示当前参数
void config_print_save_result(void);                // 显示保存结果
void config_print_read_result(void);                // 显示读取结果
void config_print_ini_not_found(void);              // 显示INI文件未找到
void config_print_invalid_input(const char *param); // 显示参数无效信息

// 工具接口 - 提供通用的配置工具函数
void config_reset_to_default(void);                 // 重置为默认配置
int config_validate_ratio(float value);             // 验证变比参数有效性
int config_validate_limit(float value);             // 验证阈值参数有效性
int config_validate_sampling_cycle(uint32_t cycle); // 验证采样周期参数有效性
uint32_t config_calculate_crc(const config_params_t *params); // 计算CRC校验值

#endif // __CONFIG_APP_H__
