#ifndef __SELFTEST_APP_H__
#define __SELFTEST_APP_H__

#include "system.h"

// 自检状态定义 - 低耦合设计，便于扩展
#define SELFTEST_STATUS_OK      1   // 检测成功
#define SELFTEST_STATUS_ERROR   0   // 检测失败

// Flash检测相关定义
#define FLASH_ID_INVALID        0x00000000  // 无效Flash ID
#define FLASH_ID_ERROR          0xFFFFFFFF  // 错误Flash ID

// TF卡容量单位定义
#define TFCARD_SIZE_UNIT_KB     1024        // KB单位转换

// RTC时间字符串长度定义
#define RTC_TIME_STRING_LEN     32          // RTC时间字符串最大长度

// 硬件检测结果结构体 - 封装各硬件检测状态和数据
typedef struct {
    // Flash检测结果
    uint8_t flash_status;       // Flash检测状态 (SELFTEST_STATUS_OK/ERROR)
    uint32_t flash_id;          // Flash ID值
    
    // TF卡检测结果  
    uint8_t tfcard_status;      // TF卡检测状态 (SELFTEST_STATUS_OK/ERROR)
    uint32_t tfcard_size_kb;    // TF卡容量(KB)
    
    // RTC检测结果
    uint8_t rtc_status;         // RTC检测状态 (通常为OK)
    char rtc_time_str[RTC_TIME_STRING_LEN]; // 格式化的时间字符串
} selftest_result_t;

// 主自检接口 - 对外提供的统一入口
void system_selftest(void);    // 系统自检主函数

// 硬件检测接口 - 低耦合设计，各硬件检测独立
uint8_t flash_selftest(uint32_t *flash_id);                    // Flash检测接口
uint8_t tfcard_selftest(uint32_t *tfcard_size_kb);             // TF卡检测接口  
uint8_t rtc_selftest(char *time_str, size_t str_len);          // RTC检测接口

// 结果处理接口 - 分离输出逻辑，便于扩展不同输出方式
void selftest_print_result(const selftest_result_t *result);   // 格式化输出结果

// 工具接口 - 提供通用的检测工具函数
void selftest_init_result(selftest_result_t *result);          // 初始化结果结构体
uint8_t selftest_is_all_ok(const selftest_result_t *result);   // 检查是否全部检测通过

#endif // __SELFTEST_APP_H__
