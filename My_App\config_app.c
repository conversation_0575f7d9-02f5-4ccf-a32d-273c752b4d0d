#include "config_app.h"
#include "lfs_util.h"  // 包含LittleFS的CRC函数声明

// 全局配置参数 - 运行时配置数据
static config_params_t g_config_params;

// 配置模块初始化 - 设置默认值，低耦合设计
int config_init(void)
{
    // 初始化配置参数为默认值
    g_config_params.magic = CONFIG_MAGIC_NUMBER;
    g_config_params.ratio = CONFIG_DEFAULT_RATIO;
    g_config_params.limit = CONFIG_DEFAULT_LIMIT;
    g_config_params.sampling_cycle = CONFIG_DEFAULT_SAMPLING_CYCLE;
    g_config_params.version = CONFIG_VERSION;
    
    // 计算并设置CRC校验值
    g_config_params.crc = config_calculate_crc(&g_config_params);
    
    return CONFIG_STATUS_OK;
}

// 获取当前配置参数 - 提供只读访问接口
void config_get_current(config_params_t *params)
{
    if (params == NULL) {
        return;
    }
    
    // 复制当前配置到输出参数
    *params = g_config_params;
}

// 设置变比参数 - 包含参数验证，低耦合设计
int config_set_ratio(float value)
{
    // 验证参数范围
    if (config_validate_ratio(value) != CONFIG_STATUS_OK) {
        return CONFIG_STATUS_ERROR;
    }
    
    // 更新配置参数
    g_config_params.ratio = value;
    
    // 重新计算CRC校验值
    g_config_params.crc = config_calculate_crc(&g_config_params);
    
    return CONFIG_STATUS_OK;
}

// 设置阈值参数 - 包含参数验证，低耦合设计
int config_set_limit(float value)
{
    // 验证参数范围
    if (config_validate_limit(value) != CONFIG_STATUS_OK) {
        return CONFIG_STATUS_ERROR;
    }
    
    // 更新配置参数
    g_config_params.limit = value;
    
    // 重新计算CRC校验值
    g_config_params.crc = config_calculate_crc(&g_config_params);
    
    return CONFIG_STATUS_OK;
}

// 获取当前变比值 - 简单的访问接口
float config_get_ratio(void)
{
    return g_config_params.ratio;
}

// 获取当前阈值 - 简单的访问接口
float config_get_limit(void)
{
    return g_config_params.limit;
}

// 设置采样周期参数 - 包含参数验证，低耦合设计
int config_set_sampling_cycle(uint32_t cycle)
{
    // 验证参数范围
    if (config_validate_sampling_cycle(cycle) != CONFIG_STATUS_OK) {
        return CONFIG_STATUS_ERROR;
    }

    // 更新配置参数
    g_config_params.sampling_cycle = cycle;

    // 重新计算CRC校验值
    g_config_params.crc = config_calculate_crc(&g_config_params);

    return CONFIG_STATUS_OK;
}

// 获取当前采样周期 - 简单的访问接口
uint32_t config_get_sampling_cycle(void)
{
    return g_config_params.sampling_cycle;
}

// 重置为默认配置 - 工具函数，便于系统恢复
void config_reset_to_default(void)
{
    config_init();  // 重用初始化函数
}

// 验证变比参数有效性 - 独立的验证逻辑
int config_validate_ratio(float value)
{
    if (value < CONFIG_RATIO_MIN || value > CONFIG_RATIO_MAX) {
        return CONFIG_STATUS_ERROR;
    }
    return CONFIG_STATUS_OK;
}

// 验证阈值参数有效性 - 独立的验证逻辑
int config_validate_limit(float value)
{
    if (value < CONFIG_LIMIT_MIN || value > CONFIG_LIMIT_MAX) {
        return CONFIG_STATUS_ERROR;
    }
    return CONFIG_STATUS_OK;
}

// 验证采样周期参数有效性 - 独立的验证逻辑
int config_validate_sampling_cycle(uint32_t cycle)
{
    // 检查是否为支持的采样周期值
    if (cycle == CONFIG_SAMPLING_CYCLE_5S ||
        cycle == CONFIG_SAMPLING_CYCLE_10S ||
        cycle == CONFIG_SAMPLING_CYCLE_15S) {
        return CONFIG_STATUS_OK;
    }
    return CONFIG_STATUS_ERROR;
}

// 计算配置数据CRC校验值 - 重用LittleFS的CRC算法
uint32_t config_calculate_crc(const config_params_t *params)
{
    if (params == NULL) {
        return 0;
    }
    
    // 计算除CRC字段外的所有数据的校验值
    // 注意：不包含CRC字段本身，避免循环依赖
    size_t data_size = sizeof(config_params_t) - sizeof(uint32_t);
    
    // 使用LittleFS的CRC32算法，初始值为0xFFFFFFFF
    return lfs_crc(0xFFFFFFFF, params, data_size);
}

// 保存配置到Flash - 支持掉电保存，低耦合设计
int config_save_to_flash(void)
{
    uint8_t write_buffer[CONFIG_FLASH_SIZE];
    config_params_t temp_config;

    // 获取当前配置并重新计算CRC
    temp_config = g_config_params;
    temp_config.crc = config_calculate_crc(&temp_config);

    // 准备写入缓冲区
    memset(write_buffer, 0xFF, CONFIG_FLASH_SIZE);  // 初始化为0xFF
    memcpy(write_buffer, &temp_config, sizeof(config_params_t));

    // 擦除Flash扇区 - 重用现有Flash驱动接口
    spi_flash_sector_erase(CONFIG_FLASH_BASE_ADDR);

    // 写入配置数据到Flash
    spi_flash_buffer_write(write_buffer, CONFIG_FLASH_BASE_ADDR, CONFIG_FLASH_SIZE);

    return CONFIG_STATUS_OK;
}

// 从Flash加载配置 - 包含数据校验和错误恢复
int config_load_from_flash(void)
{
    uint8_t read_buffer[CONFIG_FLASH_SIZE];
    config_params_t loaded_config;
    uint32_t calculated_crc;

    // 从Flash读取配置数据
    spi_flash_buffer_read(read_buffer, CONFIG_FLASH_BASE_ADDR, CONFIG_FLASH_SIZE);

    // 复制到配置结构体
    memcpy(&loaded_config, read_buffer, sizeof(config_params_t));

    // 验证魔数
    if (loaded_config.magic != CONFIG_MAGIC_NUMBER) {
        // 魔数不匹配，使用默认配置
        config_init();
        return CONFIG_STATUS_ERROR;
    }

    // 验证版本号（简单检查，后续可扩展版本兼容性）
    if (loaded_config.version != CONFIG_VERSION) {
        // 版本不匹配，使用默认配置
        config_init();
        return CONFIG_STATUS_ERROR;
    }

    // 验证CRC校验值
    calculated_crc = config_calculate_crc(&loaded_config);
    if (calculated_crc != loaded_config.crc) {
        // CRC校验失败，数据损坏，使用默认配置
        config_init();
        return CONFIG_STATUS_ERROR;
    }

    // 验证参数范围
    if (config_validate_ratio(loaded_config.ratio) != CONFIG_STATUS_OK ||
        config_validate_limit(loaded_config.limit) != CONFIG_STATUS_OK ||
        config_validate_sampling_cycle(loaded_config.sampling_cycle) != CONFIG_STATUS_OK) {
        // 参数超出范围，使用默认配置
        config_init();
        return CONFIG_STATUS_ERROR;
    }

    // 所有验证通过，更新当前配置
    g_config_params = loaded_config;

    return CONFIG_STATUS_OK;
}

// 从INI文件读取配置 - 支持[Ratio]和[Limit]段落解析，低耦合设计
int config_read_from_ini(void)
{
    FRESULT res;
    FIL ini_file;
    char line_buffer[CONFIG_INI_LINE_MAX];
    float ratio_value = CONFIG_DEFAULT_RATIO;
    float limit_value = CONFIG_DEFAULT_LIMIT;
    int ratio_found = 0, limit_found = 0;
    int current_section = 0; // 0:无, 1:[Ratio], 2:[Limit]

    // 尝试挂载TF卡 - 重用现有FATFS接口
    res = f_mount(&SDFatFS, SDPath, 1);
    if (res != FR_OK) {
        return CONFIG_STATUS_ERROR;  // TF卡挂载失败
    }

    // 尝试打开config.ini文件
    res = f_open(&ini_file, CONFIG_INI_FILENAME, FA_READ);
    if (res != FR_OK) {
        // 文件不存在，卸载文件系统
        f_mount(NULL, SDPath, 0);
        return CONFIG_STATUS_ERROR;  // 文件不存在
    }

    // 逐行读取并解析INI文件
    while (f_gets(line_buffer, CONFIG_INI_LINE_MAX, &ini_file) != NULL) {
        // 去除行尾的换行符和回车符
        char *p = line_buffer;
        while (*p && *p != '\r' && *p != '\n') p++;
        *p = '\0';

        // 跳过空行和注释行
        if (strlen(line_buffer) == 0 || line_buffer[0] == ';' || line_buffer[0] == '#') {
            continue;
        }

        // 检查段落标签
        if (line_buffer[0] == '[') {
            if (strstr(line_buffer, "[Ratio]") != NULL) {
                current_section = 1;  // 进入Ratio段落
            } else if (strstr(line_buffer, "[Limit]") != NULL) {
                current_section = 2;  // 进入Limit段落
            } else {
                current_section = 0;  // 其他段落
            }
            continue;
        }

        // 解析键值对 ChO=value
        char *equal_pos = strchr(line_buffer, '=');
        if (equal_pos != NULL) {
            *equal_pos = '\0';  // 分割键和值
            char *key = line_buffer;
            char *value = equal_pos + 1;

            // 去除键和值的前后空格
            while (*key == ' ' || *key == '\t') key++;
            while (*value == ' ' || *value == '\t') value++;

            // 检查是否为ChO键
            if (strcmp(key, "ChO") == 0) {
                float parsed_value = atof(value);

                if (current_section == 1) {  // Ratio段落
                    ratio_value = parsed_value;
                    ratio_found = 1;
                } else if (current_section == 2) {  // Limit段落
                    limit_value = parsed_value;
                    limit_found = 1;
                }
            }
        }
    }

    // 关闭文件并卸载文件系统
    f_close(&ini_file);
    f_mount(NULL, SDPath, 0);

    // 检查是否找到了必要的参数
    if (!ratio_found || !limit_found) {
        return CONFIG_STATUS_ERROR;  // 参数不完整
    }

    // 验证参数范围并更新配置
    if (config_validate_ratio(ratio_value) == CONFIG_STATUS_OK &&
        config_validate_limit(limit_value) == CONFIG_STATUS_OK) {
        // 参数有效，更新配置
        g_config_params.ratio = ratio_value;
        g_config_params.limit = limit_value;
        g_config_params.crc = config_calculate_crc(&g_config_params);
        return CONFIG_STATUS_OK;
    }

    return CONFIG_STATUS_ERROR;  // 参数超出范围
}

// 全局变量 - 记录当前交互状态
static uart_state_t g_current_input_state = UART_STATE_IDLE;

// 开始ratio输入流程 - 交互式输入接口
void config_start_ratio_input(void)
{
    g_current_input_state = UART_STATE_WAIT_RATIO;
    // 显示当前ratio值和输入提示
    my_printf(&huart1, "Ratio = %.1f\r\n", config_get_ratio());
    my_printf(&huart1, "Input value(0~100):");
}

// 开始limit输入流程 - 交互式输入接口
void config_start_limit_input(void)
{
    g_current_input_state = UART_STATE_WAIT_LIMIT;
    // 显示当前limit值和输入提示
    my_printf(&huart1, "Limit = %.1f\r\n", config_get_limit());
    my_printf(&huart1, "Input value(0~500):");
}

// 处理用户输入 - 状态机核心处理函数
void config_process_input(const char *input)
{
    if (input == NULL) {
        g_current_input_state = UART_STATE_IDLE;
        return;
    }

    // 去除输入字符串的前后空格和换行符
    char *trimmed_input = (char*)input;
    while (*trimmed_input == ' ' || *trimmed_input == '\t') trimmed_input++;

    // 去除尾部的换行符和回车符
    char *end = trimmed_input + strlen(trimmed_input) - 1;
    while (end > trimmed_input && (*end == '\r' || *end == '\n' || *end == ' ' || *end == '\t')) {
        *end = '\0';
        end--;
    }

    // 转换用户输入为浮点数
    float input_value = atof(trimmed_input);

    // 根据当前状态处理输入
    if (g_current_input_state == UART_STATE_WAIT_RATIO) {
        // 处理ratio输入
        if (config_set_ratio(input_value) == CONFIG_STATUS_OK) {
            my_printf(&huart1, "ratio modified success\r\n");
            my_printf(&huart1, "Ratio = %.1f\r\n", config_get_ratio());
        } else {
            my_printf(&huart1, "ratio invalid\r\n");
            my_printf(&huart1, "Ratio = %.1f\r\n", config_get_ratio());
        }
    } else if (g_current_input_state == UART_STATE_WAIT_LIMIT) {
        // 处理limit输入
        if (config_set_limit(input_value) == CONFIG_STATUS_OK) {
            my_printf(&huart1, "limit modified success\r\n");
            my_printf(&huart1, "limit = %.2f\r\n", config_get_limit());
        } else {
            my_printf(&huart1, "limit invalid\r\n");
            my_printf(&huart1, "limit = %.1f\r\n", config_get_limit());
        }
    }

    // 处理完成，返回空闲状态
    g_current_input_state = UART_STATE_IDLE;
}

// 显示当前参数 - 格式化输出接口
void config_print_current_params(void)
{
    my_printf(&huart1, "ratio: %.1f\r\n", config_get_ratio());
    my_printf(&huart1, "limit: %.2f\r\n", config_get_limit());
}

// 显示保存操作结果 - 严格按照需求格式
void config_print_save_result(void)
{
    my_printf(&huart1, "ratio: %.1f\r\n", config_get_ratio());
    my_printf(&huart1, "limit: %.2f\r\n", config_get_limit());
    my_printf(&huart1, "save parameters to flash\r\n");
}

// 显示读取操作结果 - 严格按照需求格式
void config_print_read_result(void)
{
    my_printf(&huart1, "read parameters from flash\r\n");
    my_printf(&huart1, "ratio: %.1f\r\n", config_get_ratio());
    my_printf(&huart1, "limit: %.2f\r\n", config_get_limit());
}

// 显示INI文件未找到信息 - 严格按照需求格式
void config_print_ini_not_found(void)
{
    my_printf(&huart1, "config.ini file not found.\r\n");
}

// 显示参数无效信息 - 通用错误提示
void config_print_invalid_input(const char *param)
{
    if (param != NULL) {
        my_printf(&huart1, "%s invalid\r\n", param);
    } else {
        my_printf(&huart1, "parameter invalid\r\n");
    }
}
