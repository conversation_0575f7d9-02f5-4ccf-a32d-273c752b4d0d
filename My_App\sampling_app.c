#include "sampling_app.h"
#include "adc_app.h"

// 全局采样上下文变量 - 运行时采样控制数据
static sampling_context_t g_sampling_context;

// 采样模块初始化 - 设置默认值，低耦合设计
int sampling_init(void)
{
    // 初始化采样上下文为默认值
    g_sampling_context.state = SAMPLING_STATE_IDLE;
    g_sampling_context.cycle_seconds = config_get_sampling_cycle();
    g_sampling_context.last_sample_time = 0;
    g_sampling_context.last_led_toggle_time = 0;
    g_sampling_context.current_voltage = 0.0f;
    g_sampling_context.overlimit_flag = 0;
    g_sampling_context.led1_state = 0;
    
    // 确保LED初始状态正确
    sampling_led1_off();
    sampling_led2_set(0);
    
    return SAMPLING_STATUS_OK;
}

// 启动采样 - 状态控制函数，低耦合设计
int sampling_start(void)
{
    // 检查当前状态，避免重复启动
    if (g_sampling_context.state == SAMPLING_STATE_RUNNING) {
        return SAMPLING_STATUS_OK;  // 已经在运行状态
    }
    
    // 更新状态和时间戳
    g_sampling_context.state = SAMPLING_STATE_RUNNING;
    g_sampling_context.last_sample_time = HAL_GetTick();
    g_sampling_context.last_led_toggle_time = HAL_GetTick();
    
    // 同步采样周期配置
    g_sampling_context.cycle_seconds = config_get_sampling_cycle();
    
    // 输出启动信息
    sampling_print_start_message();
    
    return SAMPLING_STATUS_OK;
}

// 停止采样 - 状态控制函数，低耦合设计
int sampling_stop(void)
{
    // 检查当前状态
    if (g_sampling_context.state == SAMPLING_STATE_IDLE) {
        return SAMPLING_STATUS_OK;  // 已经在停止状态
    }
    
    // 更新状态
    g_sampling_context.state = SAMPLING_STATE_IDLE;
    
    // 关闭LED指示
    sampling_led1_off();
    sampling_led2_set(0);
    
    // 重置超限标志
    g_sampling_context.overlimit_flag = 0;
    
    // 输出停止信息
    sampling_print_stop_message();
    
    return SAMPLING_STATUS_OK;
}

// 获取当前采样状态 - 简单的状态查询接口
sampling_state_t sampling_get_state(void)
{
    return g_sampling_context.state;
}

// 翻转采样状态 - 支持按键控制，低耦合设计
void sampling_toggle_state(void)
{
    if (g_sampling_context.state == SAMPLING_STATE_IDLE) {
        sampling_start();
    } else {
        sampling_stop();
    }
}

// 设置采样周期 - 周期管理接口，支持动态调整
int sampling_set_cycle(uint32_t cycle_seconds)
{
    // 验证周期有效性
    if (sampling_validate_cycle(cycle_seconds) != SAMPLING_STATUS_OK) {
        return SAMPLING_STATUS_ERROR;
    }
    
    // 更新配置系统中的采样周期
    if (config_set_sampling_cycle(cycle_seconds) != CONFIG_STATUS_OK) {
        return SAMPLING_STATUS_ERROR;
    }
    
    // 同步到采样上下文
    g_sampling_context.cycle_seconds = cycle_seconds;
    
    // 输出周期调整信息
    sampling_print_cycle_adjust(cycle_seconds);
    
    return SAMPLING_STATUS_OK;
}

// 获取当前采样周期 - 简单的周期查询接口
uint32_t sampling_get_cycle(void)
{
    return g_sampling_context.cycle_seconds;
}

// 验证采样周期有效性 - 独立的验证逻辑
int sampling_validate_cycle(uint32_t cycle)
{
    // 重用配置管理的验证逻辑
    return config_validate_sampling_cycle(cycle);
}

// 采样主任务函数 - 核心时序控制和状态管理
void sampling_task(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // LED控制逻辑 - 分离LED控制，便于扩展
    sampling_led_control(current_time);
    
    // 采样周期控制 - 仅在运行状态下执行采样
    if (g_sampling_context.state == SAMPLING_STATE_RUNNING) {
        uint32_t cycle_ms = g_sampling_context.cycle_seconds * 1000;
        
        // 检查是否到达采样时间
        if (current_time - g_sampling_context.last_sample_time >= cycle_ms) {
            // 更新电压数据
            sampling_update_voltage();
            
            // 输出采样数据
            sampling_print_data(g_sampling_context.current_voltage);
            
            // 检查超限状态
            sampling_check_overlimit(g_sampling_context.current_voltage);
            
            // 更新采样时间戳
            g_sampling_context.last_sample_time = current_time;
        }
    }
    
    // OLED显示更新
    sampling_oled_display();
}

// LED控制逻辑 - 分离LED控制，便于扩展
void sampling_led_control(uint32_t current_time)
{
    if (g_sampling_context.state == SAMPLING_STATE_RUNNING) {
        // LED1闪烁控制（1秒周期）
        if (current_time - g_sampling_context.last_led_toggle_time >= SAMPLING_LED_TOGGLE_PERIOD) {
            sampling_led1_toggle();
            g_sampling_context.last_led_toggle_time = current_time;
        }
    } else {
        // 停止状态LED1常灭
        sampling_led1_off();
    }
}

// 点亮LED1 - 重用现有LED接口
void sampling_led1_on(void)
{
    ucLed[SAMPLING_LED1_INDEX] = 1;
    g_sampling_context.led1_state = 1;
}

// 熄灭LED1 - 重用现有LED接口
void sampling_led1_off(void)
{
    ucLed[SAMPLING_LED1_INDEX] = 0;
    g_sampling_context.led1_state = 0;
}

// 翻转LED1状态 - 实现闪烁效果
void sampling_led1_toggle(void)
{
    if (g_sampling_context.led1_state) {
        sampling_led1_off();
    } else {
        sampling_led1_on();
    }
}

// 设置LED2状态 - 超限指示控制
void sampling_led2_set(uint8_t state)
{
    ucLed[SAMPLING_LED2_INDEX] = state ? 1 : 0;
}

// 获取当前电压值 - 简单的电压访问接口
float sampling_get_voltage(void)
{
    return g_sampling_context.current_voltage;
}

// 更新电压数据 - 重用ADC接口，低耦合设计
void sampling_update_voltage(void)
{
    // 调用ADC模块的电压获取接口
    g_sampling_context.current_voltage = adc_get_voltage();
}

// 检查电压超限 - 集成配置管理的limit阈值
int sampling_check_overlimit(float voltage)
{
    float limit = config_get_limit();
    
    if (voltage > limit) {
        // 电压超限
        g_sampling_context.overlimit_flag = 1;
        sampling_led2_set(1);  // 点亮LED2
        return SAMPLING_STATUS_ERROR;
    } else {
        // 电压正常
        g_sampling_context.overlimit_flag = 0;
        sampling_led2_set(0);  // 熄灭LED2
        return SAMPLING_STATUS_OK;
    }
}

// 重置采样上下文 - 工具函数，便于系统恢复
void sampling_reset_context(void)
{
    sampling_init();  // 重用初始化函数
}

// 检查是否正在采样 - 简单的状态检查接口
uint8_t sampling_is_running(void)
{
    return (g_sampling_context.state == SAMPLING_STATE_RUNNING) ? 1 : 0;
}

// 获取运行时间 - 工具函数，便于调试和监控
uint32_t sampling_get_elapsed_time(void)
{
    if (g_sampling_context.state == SAMPLING_STATE_RUNNING) {
        return HAL_GetTick() - g_sampling_context.last_sample_time;
    }
    return 0;
}

// 更新采样上下文 - 同步配置变化
void sampling_update_context(void)
{
    // 同步采样周期配置
    g_sampling_context.cycle_seconds = config_get_sampling_cycle();
}

// 输出启动信息 - 严格按照需求格式
void sampling_print_start_message(void)
{
    my_printf(&huart1, "Periodic Sampling\r\n");
    my_printf(&huart1, "sample cycle:%ds\r\n", g_sampling_context.cycle_seconds);
}

// 输出停止信息 - 严格按照需求格式
void sampling_print_stop_message(void)
{
    my_printf(&huart1, "Periodic Sampling STOP\r\n");
}

// 输出周期调整信息 - 严格按照需求格式
void sampling_print_cycle_adjust(uint32_t cycle)
{
    my_printf(&huart1, "sample cycle adjust: %ds\r\n", cycle);
}

// 输出采样数据 - 重用现有时间格式化功能
void sampling_print_data(float voltage)
{
    char time_buffer[SAMPLING_TIME_STR_LEN];

    // 获取完整时间格式
    sampling_format_time_full(time_buffer, sizeof(time_buffer));

    // 检查是否超限
    if (g_sampling_context.overlimit_flag) {
        float limit = config_get_limit();
        my_printf(&huart1, "%s %s=%.1fV OverLimit %.2f!\r\n",
                  time_buffer, SAMPLING_CHANNEL_NAME, voltage, limit);
    } else {
        my_printf(&huart1, "%s %s=%.1fV\r\n",
                  time_buffer, SAMPLING_CHANNEL_NAME, voltage);
    }
}

// 输出超限数据 - 特殊格式输出
void sampling_print_overlimit_data(float voltage, float limit)
{
    char time_buffer[SAMPLING_TIME_STR_LEN];

    // 获取完整时间格式
    sampling_format_time_full(time_buffer, sizeof(time_buffer));

    my_printf(&huart1, "%s %s=%.1fV OverLimit %.2f!\r\n",
              time_buffer, SAMPLING_CHANNEL_NAME, voltage, limit);
}

// OLED显示主函数 - 支持实时显示更新
void sampling_oled_display(void)
{
    if (g_sampling_context.state == SAMPLING_STATE_RUNNING) {
        sampling_oled_show_running();
    } else {
        sampling_oled_show_idle();
    }
}

// 显示空闲状态 - 使用u8g2直接绘制
void sampling_oled_show_idle(void)
{
    // 清除显示区域
    u8g2_ClearBuffer(&u8g2);

    // 第一行显示"system idle"
    u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr);
    u8g2_DrawStr(&u8g2, SAMPLING_OLED_TEXT_X, SAMPLING_OLED_LINE1_Y, "system idle");

    // 第二行为空

    // 发送缓冲区到OLED
    u8g2_SendBuffer(&u8g2);
}

// 显示运行状态 - 显示时间和电压
void sampling_oled_show_running(void)
{
    char time_buffer[SAMPLING_TIME_STR_LEN];
    char voltage_buffer[32];

    // 获取简短时间格式（hh:mm:ss）
    sampling_format_time_short(time_buffer, sizeof(time_buffer));

    // 格式化电压显示（xx.xxV）
    snprintf(voltage_buffer, sizeof(voltage_buffer), "%.2fV", g_sampling_context.current_voltage);

    // 清除显示区域
    u8g2_ClearBuffer(&u8g2);

    // 第一行显示时间
    u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr);
    u8g2_DrawStr(&u8g2, SAMPLING_OLED_TEXT_X, SAMPLING_OLED_LINE1_Y, time_buffer);

    // 第二行显示电压
    u8g2_DrawStr(&u8g2, SAMPLING_OLED_TEXT_X, SAMPLING_OLED_LINE2_Y, voltage_buffer);

    // 发送缓冲区到OLED
    u8g2_SendBuffer(&u8g2);
}

// 清除指定行 - 工具函数
void sampling_oled_clear_line(uint8_t line)
{
    // 这里可以实现特定行的清除逻辑
    // 当前使用完整清除和重绘的方式
    sampling_oled_display();
}

// 完整时间格式 - 重用现有时间功能
void sampling_format_time_full(char *buffer, size_t size)
{
    if (buffer == NULL || size == 0) {
        return;
    }

    RTC_TimeTypeDef current_time;
    RTC_DateTypeDef current_date;

    // 调用现有的RTC时间获取接口
    rtc_get_time_info(&current_time, &current_date);

    // 格式化为完整时间格式：2025-01-01 00:30:05
    snprintf(buffer, size, "%04d-%02d-%02d %02d:%02d:%02d",
             current_date.Year + 2000, current_date.Month, current_date.Date,
             current_time.Hours, current_time.Minutes, current_time.Seconds);
}

// 简短时间格式 - OLED显示用
void sampling_format_time_short(char *buffer, size_t size)
{
    if (buffer == NULL || size == 0) {
        return;
    }

    RTC_TimeTypeDef current_time;
    RTC_DateTypeDef current_date;

    // 调用现有的RTC时间获取接口
    rtc_get_time_info(&current_time, &current_date);

    // 格式化为简短时间格式：hh:mm:ss
    snprintf(buffer, size, "%02d:%02d:%02d",
             current_time.Hours, current_time.Minutes, current_time.Seconds);
}
