#ifndef __SAMPLING_APP_H__
#define __SAMPLING_APP_H__

#include "system.h"

// 采样控制状态定义 - 低耦合设计，便于扩展
#define SAMPLING_STATUS_OK      1   // 操作成功
#define SAMPLING_STATUS_ERROR   0   // 操作失败

// 采样周期定义 - 支持三种固定周期
#define SAMPLING_CYCLE_5S       5   // 5秒采样周期
#define SAMPLING_CYCLE_10S      10  // 10秒采样周期
#define SAMPLING_CYCLE_15S      15  // 15秒采样周期
#define SAMPLING_CYCLE_DEFAULT  SAMPLING_CYCLE_5S  // 默认采样周期

// LED控制相关定义
#define SAMPLING_LED1_INDEX     0   // LED1索引（采样指示）
#define SAMPLING_LED2_INDEX     1   // LED2索引（超限指示）
#define SAMPLING_LED_TOGGLE_PERIOD  1000  // LED1闪烁周期(毫秒)

// 采样数据格式定义
#define SAMPLING_VOLTAGE_PRECISION  1   // 电压显示精度（小数位数）
#define SAMPLING_TIME_STR_LEN      32   // 时间字符串最大长度
#define SAMPLING_CHANNEL_NAME      "ch0"  // 采样通道名称

// OLED显示相关定义
#define SAMPLING_OLED_LINE1_Y      16   // OLED第一行Y坐标
#define SAMPLING_OLED_LINE2_Y      32   // OLED第二行Y坐标
#define SAMPLING_OLED_TEXT_X       0    // OLED文本X坐标

// 采样状态枚举 - 支持状态管理和翻转
typedef enum {
    SAMPLING_STATE_IDLE = 0,    // 系统空闲状态
    SAMPLING_STATE_RUNNING      // 采样运行状态
} sampling_state_t;

// 采样上下文结构体 - 封装所有采样控制数据，便于管理和扩展
typedef struct {
    sampling_state_t state;         // 当前采样状态
    uint32_t cycle_seconds;         // 采样周期(秒)
    uint32_t last_sample_time;      // 上次采样时间戳(毫秒)
    uint32_t last_led_toggle_time;  // 上次LED翻转时间戳(毫秒)
    float current_voltage;          // 当前电压值
    uint8_t overlimit_flag;         // 超限标志位
    uint8_t led1_state;             // LED1当前状态
} sampling_context_t;

// 采样控制核心接口 - 低耦合设计，提供标准化接口
int sampling_init(void);                           // 初始化采样模块
int sampling_start(void);                          // 启动采样
int sampling_stop(void);                           // 停止采样
sampling_state_t sampling_get_state(void);         // 获取当前状态
void sampling_toggle_state(void);                  // 翻转采样状态

// 采样周期管理接口 - 独立的周期控制，支持动态调整
int sampling_set_cycle(uint32_t cycle_seconds);    // 设置采样周期
uint32_t sampling_get_cycle(void);                 // 获取当前周期
int sampling_validate_cycle(uint32_t cycle);       // 验证周期有效性

// 采样任务接口 - 主要的任务执行函数
void sampling_task(void);                          // 采样主任务函数

// LED控制接口 - 分离LED控制逻辑，便于扩展
void sampling_led_control(uint32_t current_time);  // LED控制逻辑
void sampling_led1_on(void);                       // 点亮LED1
void sampling_led1_off(void);                      // 熄灭LED1
void sampling_led1_toggle(void);                   // 翻转LED1状态
void sampling_led2_set(uint8_t state);             // 设置LED2状态

// 数据采集接口 - 电压采样和处理
float sampling_get_voltage(void);                  // 获取当前电压值
void sampling_update_voltage(void);                // 更新电压数据
int sampling_check_overlimit(float voltage);       // 检查电压超限

// 格式化输出接口 - 分离输出逻辑，便于扩展不同输出方式
void sampling_print_start_message(void);           // 输出启动信息
void sampling_print_stop_message(void);            // 输出停止信息
void sampling_print_cycle_adjust(uint32_t cycle);  // 输出周期调整信息
void sampling_print_data(float voltage);           // 输出采样数据
void sampling_print_overlimit_data(float voltage, float limit); // 输出超限数据

// OLED显示接口 - 支持实时显示更新
void sampling_oled_display(void);                  // OLED显示主函数
void sampling_oled_show_idle(void);                // 显示空闲状态
void sampling_oled_show_running(void);             // 显示运行状态
void sampling_oled_clear_line(uint8_t line);       // 清除指定行

// 时间格式化接口 - 重用现有时间功能，提供不同格式
void sampling_format_time_full(char *buffer, size_t size);     // 完整时间格式
void sampling_format_time_short(char *buffer, size_t size);    // 简短时间格式

// 工具接口 - 提供通用的采样工具函数
void sampling_reset_context(void);                 // 重置采样上下文
uint8_t sampling_is_running(void);                 // 检查是否正在采样
uint32_t sampling_get_elapsed_time(void);          // 获取运行时间
void sampling_update_context(void);                // 更新采样上下文

#endif // __SAMPLING_APP_H__
