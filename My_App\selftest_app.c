#include "selftest_app.h"

// 系统自检主函数 - 核心协调函数，低耦合设计
void system_selftest(void)
{
    selftest_result_t result;

    // 初始化结果结构体
    selftest_init_result(&result);

    // 依次执行各硬件检测 - 各检测功能相互独立
    // 1. Flash检测
    result.flash_status = flash_selftest(&result.flash_id);

    // 2. TF卡检测
    result.tfcard_status = tfcard_selftest(&result.tfcard_size_kb);

    // 3. RTC检测
    result.rtc_status = rtc_selftest(result.rtc_time_str, RTC_TIME_STRING_LEN);

    // 输出自检结果 - 分离输出逻辑，便于扩展
    selftest_print_result(&result);
}

// TF卡检测功能实现 - 低耦合设计，独立可测试
uint8_t tfcard_selftest(uint32_t *tfcard_size_kb)
{
    // 参数有效性检查
    if (tfcard_size_kb == NULL) {
        return SELFTEST_STATUS_ERROR;
    }

    FRESULT res;
    FATFS *fs;
    DWORD free_clusters;

    // 初始化返回值
    *tfcard_size_kb = 0;

    // 尝试挂载TF卡 - 重用现有FATFS接口
    res = f_mount(&SDFatFS, SDPath, 1);
    if (res != FR_OK) {
        return SELFTEST_STATUS_ERROR;  // TF卡挂载失败
    }

    // 获取TF卡容量信息
    res = f_getfree(SDPath, &free_clusters, &fs);
    if (res != FR_OK) {
        // 挂载失败后需要卸载，确保系统状态正常
        f_mount(NULL, SDPath, 0);
        return SELFTEST_STATUS_ERROR;  // 获取容量失败
    }

    // 计算总容量并转换为KB单位
    // 总容量 = (总簇数 - 2) * 每簇扇区数 * 扇区大小 / 1024
    uint32_t total_sectors = (fs->n_fatent - 2) * fs->csize;
    *tfcard_size_kb = total_sectors / 2;  // 扇区大小通常为512字节，512/1024=1/2

    // 检测完成后卸载，避免影响其他功能
    f_mount(NULL, SDPath, 0);

    return SELFTEST_STATUS_OK;  // TF卡检测成功
}

// RTC时间检测功能实现 - 低耦合设计，独立可测试
uint8_t rtc_selftest(char *time_str, size_t str_len)
{
    // 参数有效性检查
    if (time_str == NULL || str_len == 0) {
        return SELFTEST_STATUS_ERROR;
    }

    RTC_TimeTypeDef current_time;
    RTC_DateTypeDef current_date;

    // 初始化时间字符串
    memset(time_str, 0, str_len);

    // 调用现有的RTC时间获取接口
    rtc_get_time_info(&current_time, &current_date);

    // 调用现有的时间格式化接口
    format_time_output(&current_time, &current_date, time_str, str_len);

    // 检查格式化结果是否有效
    if (strlen(time_str) == 0) {
        return SELFTEST_STATUS_ERROR;  // 时间获取失败
    }

    return SELFTEST_STATUS_OK;  // RTC检测成功
}

// 自检结果格式化输出功能 - 低耦合设计，支持不同输出方式扩展
void selftest_print_result(const selftest_result_t *result)
{
    // 参数有效性检查
    if (result == NULL) {
        return;
    }

    // 输出自检开始标识
    my_printf(&huart1, "===system selftest======\r\n");

    // 输出Flash检测结果
    if (result->flash_status == SELFTEST_STATUS_OK) {
        my_printf(&huart1, "flash...ok\r\n");
    } else {
        my_printf(&huart1, "flash...error\r\n");
    }

    // 输出TF卡检测结果
    if (result->tfcard_status == SELFTEST_STATUS_OK) {
        my_printf(&huart1, "TF card...ok\r\n");
    } else {
        my_printf(&huart1, "TF card...error\r\n");
    }

    // 输出Flash ID（十六进制格式）
    my_printf(&huart1, "flash ID: 0x%06X\r\n", result->flash_id);

    // 输出TF卡容量或错误信息
    if (result->tfcard_status == SELFTEST_STATUS_OK) {
        my_printf(&huart1, "TF card memory: %lu KB\r\n", result->tfcard_size_kb);
    } else {
        my_printf(&huart1, "can not find TF card\r\n");
    }

    // 输出RTC时间
    my_printf(&huart1, "RTC: %s\r\n", result->rtc_time_str);

    // 输出自检结束标识
    my_printf(&huart1, "======system selftest======\r\n");
}

// Flash检测功能实现 - 低耦合设计，独立可测试
uint8_t flash_selftest(uint32_t *flash_id)
{
    // 参数有效性检查
    if (flash_id == NULL) {
        return SELFTEST_STATUS_ERROR;
    }
    
    // 调用现有的Flash ID读取接口
    *flash_id = spi_flash_read_id();
    
    // 判断Flash ID有效性 - 低耦合设计，检测逻辑独立
    // 有效ID：不为0且不为0xFFFFFF
    if ((*flash_id == FLASH_ID_INVALID) || (*flash_id == FLASH_ID_ERROR)) {
        return SELFTEST_STATUS_ERROR;  // Flash检测失败
    }
    
    return SELFTEST_STATUS_OK;  // Flash检测成功
}

// 工具函数：初始化结果结构体 - 便于扩展和维护
void selftest_init_result(selftest_result_t *result)
{
    if (result == NULL) {
        return;
    }
    
    // 初始化所有字段为默认值
    result->flash_status = SELFTEST_STATUS_ERROR;
    result->flash_id = FLASH_ID_INVALID;
    result->tfcard_status = SELFTEST_STATUS_ERROR;
    result->tfcard_size_kb = 0;
    result->rtc_status = SELFTEST_STATUS_ERROR;
    memset(result->rtc_time_str, 0, RTC_TIME_STRING_LEN);
}

// 工具函数：检查是否全部检测通过 - 便于统一状态判断
uint8_t selftest_is_all_ok(const selftest_result_t *result)
{
    if (result == NULL) {
        return SELFTEST_STATUS_ERROR;
    }
    
    // 检查所有硬件检测状态
    if ((result->flash_status == SELFTEST_STATUS_OK) &&
        (result->tfcard_status == SELFTEST_STATUS_OK) &&
        (result->rtc_status == SELFTEST_STATUS_OK)) {
        return SELFTEST_STATUS_OK;
    }
    
    return SELFTEST_STATUS_ERROR;
}
