Dependencies for Project 'DEMO_test', Target 'DEMO_test': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (startup_stm32f429xx.s)(0x68510CB5)(--target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4 -Wa,armasm,--pd,"__MICROLIB SETA 1"

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 541" -Wa,armasm,--pd,"GD32F470 SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o demo_test/startup_stm32f429xx.o)
F (../Core/Src/main.c)(0x68510E03)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/main.o -MMD)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\Core\Inc\dma.h)(0x68444CAA)
I (..\FATFS\App\fatfs.h)(0x6850C312)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681A1A29)
I (..\FATFS\Target\sd_diskio.h)(0x6850C312)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\rtc.h)(0x68510CB2)
I (..\Core\Inc\sdio.h)(0x6850C313)
I (..\Core\Inc\spi.h)(0x68503348)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\gpio.h)(0x683E8E06)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\My_App\system.h)(0x68510D44)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
F (../Core/Src/gpio.c)(0x6850C30D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/gpio.o -MMD)
I (..\Core\Inc\gpio.h)(0x683E8E06)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/adc.c)(0x684A830A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/adc.o -MMD)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/dac.c)(0x684A830A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/dac.o -MMD)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/dma.c)(0x684A830B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/dma.o -MMD)
I (..\Core\Inc\dma.h)(0x68444CAA)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/i2c.c)(0x684E8372)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/i2c.o -MMD)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/rtc.c)(0x68510CB2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/rtc.o -MMD)
I (..\Core\Inc\rtc.h)(0x68510CB2)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/sdio.c)(0x6850FA52)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/sdio.o -MMD)
I (..\Core\Inc\sdio.h)(0x6850C313)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/spi.c)(0x68503348)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/spi.o -MMD)
I (..\Core\Inc\spi.h)(0x68503348)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/tim.c)(0x684AC916)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/tim.o -MMD)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/usart.c)(0x68499C03)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/usart.o -MMD)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
F (../Core/Src/stm32f4xx_it.c)(0x684A830B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_it.o -MMD)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_it.h)(0x684A830B)
F (../Core/Src/stm32f4xx_hal_msp.c)(0x683E8E07)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_msp.o -MMD)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_adc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_adc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_ll_adc.o -MMD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_rcc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_rcc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_flash.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_flash_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_flash_ramfunc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_gpio.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_dma_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_dma.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_pwr.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_pwr_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_cortex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_exti.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_dac.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_dac_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_i2c.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_i2c_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_rtc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_rtc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_sdmmc.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_ll_sdmmc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sd.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_sd.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_spi.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_tim.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_tim_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x681A1A69)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/stm32f4xx_hal_uart.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../Core/Src/system_stm32f4xx.c)(0x681A1A65)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/system_stm32f4xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (..\Components\ebtn\bit_array.h)(0x68030431)()
F (..\Components\ebtn\ebtn.c)(0x6815A37B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/ebtn.o -MMD)
I (..\Components\ebtn\ebtn.h)(0x6815A2C5)
I (..\Components\ebtn\bit_array.h)(0x68030431)
F (..\Components\ebtn\ebtn.h)(0x6815A2C5)()
F (..\Components\ringbuffer\ringbuffer.c)(0x68498562)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/ringbuffer.o -MMD)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
F (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)()
F (..\Components\Oled\oled.c)(0x6818979C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/oled.o -MMD)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Components\Oled\oledfont.h)(0x6819A2DD)
I (..\Core\Inc\i2c.h)(0x684E8372)
F (..\Components\u8g2\mui.c)(0x6818ABD1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/mui.o -MMD)
I (..\Components\u8g2\mui.h)(0x6818ABD2)
F (..\Components\u8g2\mui_u8g2.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/mui_u8g2.o -MMD)
I (..\Components\u8g2\mui.h)(0x6818ABD2)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\u8g2\mui_u8g2.h)(0x6818ABD2)
F (..\Components\u8g2\u8g2_arc.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_arc.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_bitmap.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_bitmap.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_box.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_box.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_buffer.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_buffer.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_button.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_button.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_circle.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_circle.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_cleardisplay.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_cleardisplay.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_d_memory.c)(0x68199C73)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_d_memory.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_d_setup.c)(0x68199C24)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_d_setup.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_font.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_font.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_fonts.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_fonts.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_hvline.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_hvline.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_input_value.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_input_value.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_intersection.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_intersection.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_kerning.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_kerning.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_line.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_line.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_ll_hvline.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_ll_hvline.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_message.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_message.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_polygon.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_polygon.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_selection_list.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_selection_list.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8g2_setup.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8g2_setup.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8log.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8log.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8log_u8g2.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8log_u8g2.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8log_u8x8.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8log_u8x8.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_8x8.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_8x8.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_byte.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_byte.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_cad.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_cad.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_capture.c)(0x6818ABD3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_capture.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_d_ssd1306_128x32.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_d_ssd1306_128x32.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_debounce.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_debounce.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_display.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_display.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_fonts.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_fonts.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_gpio.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_gpio.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_input_value.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_input_value.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_message.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_message.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_selection_list.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_selection_list.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_setup.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_setup.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_string.c)(0x6818ABD5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_string.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_u8toa.c)(0x68199D1F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_u8toa.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\u8g2\u8x8_u16toa.c)(0x68199D1F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/u8x8_u16toa.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
F (..\Components\WouoUI_Page\WouoUI.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi.o -MMD)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
F (..\Components\WouoUI_Page\WouoUI_anim.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi_anim.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
F (..\Components\WouoUI_Page\WouoUI_font.c)(0x6819A2D3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi_font.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
F (..\Components\WouoUI_Page\WouoUI_graph.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi_graph.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
F (..\Components\WouoUI_Page\WouoUI_msg.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi_msg.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
F (..\Components\WouoUI_Page\WouoUI_page.c)(0x6819A497)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi_page.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
F (..\Components\WouoUI_Page\WouoUI_user.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi_user.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
F (..\Components\WouoUI_Page\WouoUI_win.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/wououi_win.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
F (..\Components\GD25flash\gd25qxx.c)(0x681CBD48)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/gd25qxx.o -MMD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (..\Components\GD25flash\lfs.c)(0x681CBD49)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/lfs.o -MMD)
I (..\Components\GD25flash\lfs.h)(0x681CBC39)
I (..\Components\GD25flash\lfs_util.h)(0x681CBC31)
F (..\Components\GD25flash\lfs_port.c)(0x681CBD49)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/lfs_port.o -MMD)
I (..\Components\GD25flash\lfs_port.h)(0x681CBC2B)
I (..\Components\GD25flash\lfs.h)(0x681CBC39)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (..\Components\GD25flash\lfs_util.c)(0x681CBD49)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/lfs_util.o -MMD)
I (..\Components\GD25flash\lfs_util.h)(0x681CBC31)
F (../FATFS/Target/bsp_driver_sd.c)(0x6850C312)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/bsp_driver_sd.o -MMD)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
F (../FATFS/Target/sd_diskio.c)(0x6850C312)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/sd_diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
I (..\FATFS\Target\sd_diskio.h)(0x6850C312)
F (../FATFS/App/fatfs.c)(0x6850C312)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/fatfs.o -MMD)
I (..\FATFS\App\fatfs.h)(0x6850C312)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681A1A29)
I (..\FATFS\Target\sd_diskio.h)(0x6850C312)
F (../Middlewares/Third_Party/FatFs/src/diskio.c)(0x681A1A29)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
F (../Middlewares/Third_Party/FatFs/src/ff.c)(0x681A1A29)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/ff.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681A1A29)
F (../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c)(0x681A1A29)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/ff_gen_drv.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
F (../Middlewares/Third_Party/FatFs/src/option/syscall.c)(0x681A1A29)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/syscall.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\option\..\integer.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
F (../Middlewares/Third_Party/FatFs/src/option/cc936.c)(0x681A1A29)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/cc936.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\option\..\integer.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
F (..\My_App\system.h)(0x68510D44)()
F (..\My_App\scheduler.c)(0x68503A88)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/scheduler.o -MMD)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
F (..\My_App\led_app.h)(0x683EE66C)()
F (..\My_App\led_app.c)(0x684E8869)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/led_app.o -MMD)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
F (..\My_App\ebtn_app.c)(0x6850F79C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/ebtn_app.o -MMD)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
I (..\Components\ebtn\ebtn.h)(0x6815A2C5)
I (..\Components\ebtn\bit_array.h)(0x68030431)
F (..\My_App\ebtn_app.h)(0x6842D2C6)()
F (..\My_App\uart_app.c)(0x684992CF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/uart_app.o -MMD)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
F (..\My_App\uart_app.h)(0x68444A97)()
F (..\My_App\adc_app.c)(0x684A82FF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/adc_app.o -MMD)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\flash_app.h)(0x6850C5D1)
F (..\My_App\adc_app.h)(0x68499C68)()
F (..\My_App\oled_app.c)(0x685033D9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/oled_app.o -MMD)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
F (..\My_App\oled_app.h)(0x684EC004)()
F (..\My_App\flash_app.c)(0x6850C735)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/flash_app.o -MMD)
I (..\My_App\flash_app.h)(0x6850C5D1)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\FATFS\App\fatfs.h)(0x6850C312)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681A1A29)
I (..\FATFS\Target\ffconf.h)(0x6850C311)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6850C312)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681A1A29)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681A1A29)
I (..\FATFS\Target\sd_diskio.h)(0x6850C312)
F (..\My_App\flash_app.h)(0x6850C5D1)()
F (..\My_App\rtc_app.h)(0x68510D63)()
F (..\My_App\rtc_app.c)(0x684E6D90)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Components/Oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25flash -I ../My_App -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_DEMO_test

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx

-o demo_test/rtc_app.o -MMD)
I (..\My_App\rtc_app.h)(0x68510D63)
I (..\My_App\system.h)(0x68510D44)
I (..\Core\Inc\main.h)(0x6850C316)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681A1A69)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x68510CB3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681A1A69)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681A1A65)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x681A1A64)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681A1A65)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x681A1A69)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x681A1A69)
I (..\Core\Inc\usart.h)(0x6843FFDA)
I (..\Core\Inc\i2c.h)(0x684E8372)
I (..\Core\Inc\adc.h)(0x68499C03)
I (..\Core\Inc\tim.h)(0x684A830B)
I (..\Core\Inc\dac.h)(0x684A830B)
I (..\My_App\scheduler.h)(0x683E910A)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Components\Oled\oled.h)(0x60BF6F21)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD3)
I (..\Components\WouoUI_Page\WouoUI.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x684EAF80)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADD)
I (..\Components\GD25flash\gd25qxx.h)(0x681CBC1F)
I (..\My_App\oled_app.h)(0x684EC004)
I (..\My_App\led_app.h)(0x683EE66C)
I (..\My_App\ebtn_app.h)(0x6842D2C6)
I (..\My_App\uart_app.h)(0x68444A97)
I (..\My_App\adc_app.h)(0x68499C68)
I (..\My_App\flash_app.h)(0x6850C5D1)
